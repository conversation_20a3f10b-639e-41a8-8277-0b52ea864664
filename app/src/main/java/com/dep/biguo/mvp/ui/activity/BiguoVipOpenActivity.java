package com.dep.biguo.mvp.ui.activity;

import android.graphics.Color;
import android.graphics.Typeface;
import android.os.Build;
import android.animation.ValueAnimator;
import android.os.Bundle;
import android.text.Html;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.databinding.DataBindingUtil;

import com.hjq.toast.ToastUtils;
import android.view.View;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.biguo.utils.dialog.MessageDialog;
import com.jess.arms.base.BaseActivity;
import com.biguo.utils.util.AppUtil;
import com.dep.biguo.R;
import com.dep.biguo.bean.BiguoVipOpenBean;
import com.dep.biguo.bean.DiscountBean;
import com.dep.biguo.bean.TableRowData;
import com.dep.biguo.bean.UserBean;
import com.dep.biguo.common.Constant;
import com.dep.biguo.databinding.BiguoVipOpenActivityBinding;
import com.dep.biguo.di.component.DaggerBiguoVipOpenComponent;
import com.dep.biguo.dialog.RuleDialog;
import com.dep.biguo.dialog.ShareDialog;
import com.dep.biguo.mvp.contract.BiguoVipOpenContract;
import com.dep.biguo.mvp.presenter.BiguoVipOpenPresenter;
import com.dep.biguo.mvp.ui.activity.HtmlActivity;
import com.dep.biguo.mvp.ui.adapter.VipTableAdapter;
import com.dep.biguo.utils.MainAppUtils;
import com.dep.biguo.utils.StartFinal;
import com.dep.biguo.utils.TimeFormatUtils;
import com.dep.biguo.utils.mmkv.UserCache;
import com.dep.biguo.utils.pay.PayUtils;
import com.dep.biguo.dialog.DiscountPayDialog;
import com.dep.biguo.util.VipTableDataProvider;
import com.dep.biguo.widget.toolbar.NormalToolbarUtil;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.utils.ArmsUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class BiguoVipOpenActivity extends BaseActivity<BiguoVipOpenPresenter> implements BiguoVipOpenContract.View,View.OnClickListener {
    private BiguoVipOpenActivityBinding binding;
    private NormalToolbarUtil toolbarUtil;


    @Override
    public void setupActivityComponent(@NonNull AppComponent appComponent) {
        DaggerBiguoVipOpenComponent
                .builder()
                .appComponent(appComponent)
                .view(this)
                .build()
                .inject(this);
    }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.setContentView(this, R.layout.biguo_vip_open_activity);

        // Set up click listeners manually
        binding.openGroupVipLayout.setOnClickListener(this);
        binding.serviceAgreementView.setOnClickListener(this);
        binding.ruleView.setOnClickListener(this);
        binding.vipInfoLayout.setOnClickListener(this);
        binding.discountInfoLayout.setOnClickListener(this);
        binding.singlePurchaseCard.setOnClickListener(this);
        binding.groupPurchaseCard.setOnClickListener(this);

        // Set up VIP table RecyclerView
        setupVipTable();

        toolbarUtil = new NormalToolbarUtil(this)
                .setLeftDrawableRes(R.drawable.arrow_white_back)
                .setMustDay(this, true)
                .setAnchorView(binding.ruleView)
                .setCenterTextColor(Color.WHITE);
        toolbarUtil.setFollowScrollListener(binding.nestedScrollView, (toolbar, changeRate, isDay) -> {
                    //计算图标的颜色，深色模式,图标只需要白色
                    int iconRgb = Math.max(102, 255 - (int) ((changeRate) * 255));
                    int iconColor = Color.rgb(iconRgb, iconRgb, iconRgb);

                    //计算文字的颜色，深色模式,文字只需要白色 
                    int textRgb = Math.max(51, 255 - (int) ((changeRate) * 255));
                    int textColor = Color.argb(255, textRgb, textRgb, textRgb);

                    setIconAndTextAndBackgroundColor(iconColor, textColor);
                });

        String text = "我已阅读并同意<font color='#FE5656'>会员协议</font>，且知晓权益为一年有效期";
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            binding.serviceAgreementView.setText(Html.fromHtml(text, Html.FROM_HTML_MODE_LEGACY));
        } else {
            binding.serviceAgreementView.setText(Html.fromHtml(text));
        }

        updateCardSelection(true);


        return 0;
    }

    private void setupVipTable() {
        LinearLayout privilegeColumn = binding.getRoot().findViewById(R.id.privilege_column);
        LinearLayout superVipColumn = binding.getRoot().findViewById(R.id.super_vip_column);
        LinearLayout vipColumn = binding.getRoot().findViewById(R.id.vip_column);
        LinearLayout regularUserColumn = binding.getRoot().findViewById(R.id.regular_user_column);

        List<TableRowData> tableData = VipTableDataProvider.getTableData();

        for (int i = 0; i < tableData.size(); i++) {
            TableRowData rowData = tableData.get(i);
            boolean isHeader = i == 0;
            
            // Add cells to each column with proper styling
            addCellToColumn(privilegeColumn, rowData.getPrivilege(), rowData.getRowType(), isHeader, false);
            addCellToColumn(superVipColumn, rowData.getSuperVipText(), rowData.getSuperVipIcon(), rowData.getSuperVipType(), rowData.getRowType(), isHeader, true);
            addCellToColumn(vipColumn, rowData.getVipText(), rowData.getVipIcon(), rowData.getVipType(), rowData.getRowType(), isHeader, false);
            addCellToColumn(regularUserColumn, rowData.getRegularText(), rowData.getRegularIcon(), rowData.getRegularType(), rowData.getRowType(), isHeader, false);
        }
    }

    private void addCellToColumn(LinearLayout column, String text, TableRowData.RowType rowType, boolean isHeader, boolean isHighlight) {
        TextView tv = new TextView(this);
        tv.setText(text);
        tv.setGravity(Gravity.CENTER);

        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                isHeader ? dpToPx(44) : dpToPx(36)
        );
        params.setMargins(0, 0, 0, dpToPx(isHeader ? 12 : 8));
        tv.setLayoutParams(params);
        tv.setBackgroundColor(Color.TRANSPARENT);

        if (isHeader) {
            tv.setTextSize(14);
            tv.setTextColor(isHighlight ? Color.parseColor("#AD8A49") : Color.parseColor("#333333"));
            tv.setTypeface(null, Typeface.BOLD);
        } else {
            tv.setTextSize(12);
            tv.setTextColor(isHighlight ? Color.parseColor("#AD8A49") : Color.parseColor("#666666"));
        }

        column.addView(tv);
    }

    private void addCellToColumn(LinearLayout column, String text, int iconRes, TableRowData.CellType cellType, TableRowData.RowType rowType, boolean isHeader, boolean isHighlight) {
        View cellView;
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                isHeader ? dpToPx(44) : dpToPx(36)
        );
        params.setMargins(0, 0, 0, dpToPx(isHeader ? 12 : 8));


        if (cellType == TableRowData.CellType.TEXT) {
            TextView tv = new TextView(this);
            tv.setText(text);
            tv.setGravity(Gravity.CENTER);
            tv.setLayoutParams(params);

            if (isHeader) {
                tv.setTextSize(14);
                tv.setTextColor(isHighlight ? Color.parseColor("#AD8A49") : Color.parseColor("#333333"));
                tv.setTypeface(null, Typeface.BOLD);
            } else {
                tv.setTextSize(12);
                tv.setTextColor(isHighlight ? Color.parseColor("#AD8A49") : Color.parseColor("#666666"));
            }
            cellView = tv;
        } else {
            LinearLayout container = new LinearLayout(this);
            container.setGravity(Gravity.CENTER);
            container.setLayoutParams(params);
            
            ImageView iv = new ImageView(this);
            iv.setImageResource(iconRes);
            LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(dpToPx(20), dpToPx(20));
            iv.setLayoutParams(iconParams);
            
            container.addView(iv);
            cellView = container;
        }

        cellView.setBackgroundColor(Color.TRANSPARENT);
        column.addView(cellView);
    }

    private int dpToPx(int dp) {
        float density = getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }

    /**给标题栏的图标和文字着色
     * @param iconColor       图标颜色
     * @param rightTextColor  标题文字颜色
     */
    public void setIconAndTextAndBackgroundColor(int iconColor, int rightTextColor){
        toolbarUtil.setIconAndTextColor(iconColor, rightTextColor, Color.WHITE);
    }

    @Override
    public void showMessage(@NonNull String message) {
        ToastUtils.show(message);
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        if (mPresenter != null) {
            mPresenter.getData(true);
        }
    }

    public void onClick(View view){
        if(view.getId() == R.id.openGroupVipLayout){
            if(!MainAppUtils.checkLogin(BiguoVipOpenActivity.this)) return;
            BiguoVipOpenBean bean = mPresenter.getBiguoVipBean();
            String content = String.format(Locale.CHINA, "邀请1名新用户参团享半价开通折扣卡，开通后可享%.1f折优惠;24小时内未成团自动退款!", Float.parseFloat(bean.getDiscount()) * 10);

            if(mPresenter.getBiguoVipBean().getGroup_info() == null){
                new MessageDialog.Builder(getSupportFragmentManager())
                        .setTitle("温馨提示")
                        .setContent(content)
                        .setNegativeText("关闭")
                        .setPositiveText("我要购买")
                        .setPositiveClickListener(v -> mPresenter.getDiscountCard(1))
                        .builder()
                        .show();
            }else {
                BiguoVipOpenBean.GroupInfo groupInfo = bean.getGroup_info();
                String shareUrl = groupInfo.getShare_url();
                if(!shareUrl.contains("?")){
                    shareUrl += "?";
                }
                shareUrl = shareUrl
                        + (shareUrl.endsWith("?") ? "" : "&")
                        + "group_id=" + groupInfo.getGroup_id()
                        + "&users_id=" + groupInfo.getUsers_id()
                        + "&inviter_id=" + groupInfo.getUsers_id();
                new ShareDialog.Builder(getActivity())
                        .setShareTitle("您有一个好友拼团福利待领取")
                        .setShareContent("新用户注册并加入拼团，立享半价解锁笔果折扣卡！")
                        .setShareUrl(shareUrl)
                        .setOnShareListener((type) -> showMessage("分享成功，请等待被邀请用户注册并参与拼团"))
                        .builder()
                        .show();
            }

        }else if(view.getId() == R.id.serviceAgreementView){
            HtmlActivity.start(this, Constant.AGREEMENT_USER3);

        }else if(view.getId() == R.id.ruleView){
            mPresenter.getRule("membership_rule");
        } else if (view.getId() == R.id.vip_info_layout) {
            updateCardSelection(true);
        } else if (view.getId() == R.id.discount_info_layout) {
            updateCardSelection(false);
        } else if (view.getId() == R.id.singlePurchaseCard) {
            updatePurchaseCardWeight(true);
        } else if (view.getId() == R.id.groupPurchaseCard) {
            updatePurchaseCardWeight(false);
        }
    }

    @Override
    public void setDataSuccess(BiguoVipOpenBean bean) {
        //如果列表是空的，则创建一个数组
        if(AppUtil.isEmpty(bean.getEnjoy_discounts())){
            bean.setEnjoy_discounts(new ArrayList<>());
        }
        if(AppUtil.isEmpty(bean.getNot_enjoy_discounts())){
            bean.setNot_enjoy_discounts(new ArrayList<>());
        }

        // 显示价格和开通年限
        binding.priceAndValidityView.setText("AI功能畅享用");
        // 显示折扣信息
        try {
            binding.discountView.setText("折扣会员卡");
        }catch (Exception e){
            binding.discountView.setText("专享折扣价");
        }
        // 显示享受的权益数量
        binding.enjoyCountView.setText("课程折扣8.8折");

        // 处理拼团相关显示
        if(bean.getGroup_info() == null){
            binding.openGroupVipView.setText(String.format("¥%s 立即邀请", AppUtil.isEmpty(bean.getGroup_price(), "9999")));
            binding.openGroupSubView.setText("邀请1名新用户参团");
            binding.countDownTimeView.setVisibility(View.GONE);
        }else {
            binding.openGroupVipView.setText(String.format("¥%s 拼团中", AppUtil.isEmpty(bean.getGroup_price(), "9999")));
            binding.openGroupSubView.setText("邀请1名新用户参团");
            binding.countDownTimeView.setVisibility(View.VISIBLE);
        }
        binding.openCountView.setText(String.format("超级折扣会员卡享受%s大权益", 8));

    }

    public int getDiscountsCount(List<BiguoVipOpenBean.DiscountsBean> list){
        int count = 0;
        for (int i=0;i<list.size(); i++){
            if(!AppUtil.isEmpty(list.get(i).getTitle())) {
                count ++;
            }
        }
        return count;
    }

    @Override
    public void refreshCountTime(long countTime) {
        binding.countDownTimeView.setText(TimeFormatUtils.formatMillisecond(countTime));
    }

    @Override
    public void showPayDialog(int isGroup, List<DiscountBean> allDiscount) {
        BiguoVipOpenBean openBean = mPresenter.getBiguoVipBean();
        String payPrice = isGroup == StartFinal.YES ? openBean.getGroup_price() : openBean.getPrice();
        new DiscountPayDialog.Builder(this, PayUtils.MEMBERSHIP)
                .setGoodsName("笔果折扣卡")
                .setShowGuobi(false)
                .setPrice(Float.parseFloat(mPresenter.getBiguoVipBean().getPrice()))
                .setPayPrice(Float.parseFloat(AppUtil.isEmpty(payPrice, "9999")))
                .setDiscountList(allDiscount)
                .setOnPayListener((joinGroup, discount, payType) -> {
                    int coupon_id = discount == null ? 0 : discount.getId();
                    mPresenter.payOrder(payType, 0, coupon_id, isGroup, openBean.getGroup_id());
                })
                .build()
                .show();
    }

    @Override
    public AppCompatActivity getActivity() {
        return this;
    }

    @Override
    public void paySuccess() {
        showMessage("支付成功");
        UserBean userBean = UserCache.getUserCache();
        userBean.setMembership(1);
        UserCache.cacheUser(userBean);
        ArmsUtils.startActivity(BiguoVipActivity.class);
        finish();
    }

    @Override
    public void getRuleSuccess(List<String> rule) {
        new RuleDialog(this)
                .setTitleText("购买须知")
                .setRules(rule)
                .show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        if(mPresenter !=null && mPresenter.getBiguoVipBean() != null) {
            mPresenter.getData(false);
        }
    }

    @Override
    public void showLoading() {
        // TODO: 实现加载中的UI，例如显示一个ProgressBar
    }

    @Override
    public void hideLoading() {
        // TODO: 隐藏加载中的UI
    }
    public void setToolBarErrorStyle(boolean isSuccess){
        if(isSuccess){
            setIconAndTextAndBackgroundColor(Color.WHITE, Color.WHITE);
        }else {
            setIconAndTextAndBackgroundColor(AppUtil.getColorRes(this, R.color.tblack), AppUtil.getColorRes(this, R.color.tblack));
        }
    }

    private void updateCardSelection(boolean isSuperVipSelected) {
        if (isSuperVipSelected) {
            // 选中超级会员卡
            binding.superVipTitle.setTextColor(Color.parseColor("#FFE9A3"));
            binding.priceAndValidityView.setTextColor(Color.parseColor("#FFE9A3"));
            binding.discountView.setTextColor(Color.parseColor("#FFFFFF"));
            binding.enjoyCountView.setTextColor(Color.parseColor("#FFFFFF"));

            binding.leftGrainSuper.setVisibility(View.VISIBLE);
            binding.rightGrainSuper.setVisibility(View.VISIBLE);
            binding.triangleSuper.setVisibility(View.VISIBLE);

            binding.leftGrainDiscount.setVisibility(View.INVISIBLE);
            binding.rightGrainDiscount.setVisibility(View.INVISIBLE);
            binding.triangleDiscount.setVisibility(View.INVISIBLE);



        } else {
            // 选中折扣会员卡
            binding.superVipTitle.setTextColor(Color.parseColor("#FFFFFF"));
            binding.priceAndValidityView.setTextColor(Color.parseColor("#FFFFFF"));
            binding.discountView.setTextColor(Color.parseColor("#FFE9A3"));
            binding.enjoyCountView.setTextColor(Color.parseColor("#FFE9A3"));

            binding.leftGrainSuper.setVisibility(View.INVISIBLE);
            binding.rightGrainSuper.setVisibility(View.INVISIBLE);
            binding.triangleSuper.setVisibility(View.INVISIBLE);

            binding.leftGrainDiscount.setVisibility(View.VISIBLE);
            binding.rightGrainDiscount.setVisibility(View.VISIBLE);
            binding.triangleDiscount.setVisibility(View.VISIBLE);


        }
        updatePurchaseCardWeight(true);
    }

    private void updatePurchaseCardWeight(boolean isSinglePurchaseSelected) {
        final float targetWeight = 1.2f;
        final float normalWeight = 1.0f;
        final int duration = 300; // 动画时长

        LinearLayout.LayoutParams singleParams = (LinearLayout.LayoutParams) binding.singlePurchaseCard.getLayoutParams();
        LinearLayout.LayoutParams groupParams = (LinearLayout.LayoutParams) binding.groupPurchaseCard.getLayoutParams();

        float singleStartWeight = singleParams.weight;
        float groupStartWeight = groupParams.weight;

        float singleEndWeight = isSinglePurchaseSelected ? targetWeight : normalWeight;
        float groupEndWeight = isSinglePurchaseSelected ? normalWeight : targetWeight;

        // 为 singlePurchaseCard 创建动画
        ValueAnimator singleAnimator = ValueAnimator.ofFloat(singleStartWeight, singleEndWeight);
        singleAnimator.addUpdateListener(animation -> {
            singleParams.weight = (float) animation.getAnimatedValue();
            binding.singlePurchaseCard.setLayoutParams(singleParams);
        });
        singleAnimator.setDuration(duration);
        singleAnimator.start();

        // 为 groupPurchaseCard 创建动画
        ValueAnimator groupAnimator = ValueAnimator.ofFloat(groupStartWeight, groupEndWeight);
        groupAnimator.addUpdateListener(animation -> {
            groupParams.weight = (float) animation.getAnimatedValue();
            binding.groupPurchaseCard.setLayoutParams(groupParams);
        });
        groupAnimator.setDuration(duration);
        groupAnimator.start();

        if (isSinglePurchaseSelected) {
            binding.contentArea.setBackgroundResource(R.drawable.bg_promo_tag_gradingyellows);
            binding.groupPurchaseContentArea.setBackgroundResource(R.drawable.bg_promo_tag_solidyellow);
        } else {
            binding.contentArea.setBackgroundResource(R.drawable.bg_promo_tag_solidyellow);
            binding.groupPurchaseContentArea.setBackgroundResource(R.drawable.bg_promo_tag_gradingyellows);
        }
    }
}
