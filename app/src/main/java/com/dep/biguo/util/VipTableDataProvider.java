package com.dep.biguo.util;

import com.dep.biguo.R;
import com.dep.biguo.bean.TableRowData;

import java.util.ArrayList;
import java.util.List;

/**
 * Provides static data for VIP comparison table
 */
public class VipTableDataProvider {
    
    public static List<TableRowData> getTableData() {
        List<TableRowData> data = new ArrayList<>();
        
        // Header row
        data.add(new TableRowData("特权", "超级会员卡", "会员卡", "普通用户"));
        
        // Pricing rows (6 rows)
        data.add(TableRowData.createTextRow("购买VIP题库", "8.8折", "8.8折", "原价"));
        data.add(TableRowData.createTextRow("购买考前押密", "8.8折", "8.8折", "原价"));
        data.add(TableRowData.createTextRow("购买高频考点", "8.8折", "8.8折", "原价"));
        data.add(TableRowData.createTextRow("购买精讲视频", "8.8折", "8.8折", "原价"));
        data.add(TableRowData.createTextRow("购买串讲视频", "8.8折", "8.8折", "原价"));
        data.add(TableRowData.createTextRow("购买直播密训", "8.8折", "8.8折", "原价"));
        
        // Usage limit rows (2 rows)
        data.add(TableRowData.createTextRow("AI视频解析", "使用不限量", "使用5次", "使用5次"));
        data.add(TableRowData.createTextRow("AI文字解析", "使用不限量", "使用5次", "使用5次"));
        
        // Generation row (1 row)
        data.add(TableRowData.createMixedRow("真题模拟",
                TableRowData.CellType.TEXT, "生成不限量", 0,
                TableRowData.CellType.ICON, null, R.drawable.ic_cross_red,
                TableRowData.CellType.ICON, null, R.drawable.ic_cross_red));
        
        // Feature rows with icons (3 rows)
        data.add(TableRowData.createMixedRow("去除弹窗",
                TableRowData.CellType.ICON, null, R.drawable.ic_checkmark_light_green,
                TableRowData.CellType.ICON, null, R.drawable.ic_cross_red,
                TableRowData.CellType.ICON, null, R.drawable.ic_cross_red));
                
        data.add(TableRowData.createMixedRow("专享交流群",
                TableRowData.CellType.ICON, null, R.drawable.ic_checkmark_light_green,
                TableRowData.CellType.ICON, null, R.drawable.ic_checkmark_light_green,
                TableRowData.CellType.ICON, null, R.drawable.ic_cross_red));
                
        data.add(TableRowData.createMixedRow("学费减免",
                TableRowData.CellType.ICON, null, R.drawable.ic_checkmark_light_green,
                TableRowData.CellType.ICON, null, R.drawable.ic_cross_red,
                TableRowData.CellType.ICON, null, R.drawable.ic_cross_red));
        
        return data;
    }
}
