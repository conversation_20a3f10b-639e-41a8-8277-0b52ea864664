<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/nestedScrollView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:layout_constraintBottom_toTopOf="@+id/bottom_bar"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <!-- 垂直LinearLayout作为滚动内容的主容器 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 2. 购买选项卡片区域 -->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/top_banner_container"
                    android:layout_width="match_parent"
                    android:layout_height="190dp"
                    android:background="@drawable/bg_top_banner"> <!-- 假设这是一个深色带波浪纹的drawable -->

                    <ImageView
                        android:id="@+id/ruleView"
                        android:layout_width="7dp"
                        android:layout_height="14dp"
                        android:layout_marginStart="15dp"
                        android:layout_marginTop="14dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <!-- Banner内的文本信息 -->
                    <LinearLayout
                        android:id="@+id/vip_info_layout"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="40dp"
                        android:layout_marginTop="10dp"
                        android:gravity="center"
                        android:orientation="vertical"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/guideline_vertical_50"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <!-- 超级折扣会员卡标题区域，包含左右麦穗装饰 -->
                        <LinearLayout
                            android:id="@+id/superVipTitleLayout"
                            android:layout_width="wrap_content"
                            android:layout_height="22dp"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <!-- 左麦穗 -->
                            <ImageView
                                android:id="@+id/leftGrainSuper"
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_marginEnd="3dp"
                                android:src="@drawable/left_grain" />

                            <!-- 标题文字 -->
                            <TextView
                                android:id="@+id/superVipTitle"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="超级折扣会员卡"
                                android:textColor="@android:color/white"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <!-- 右麦穗 -->
                            <ImageView
                                android:id="@+id/rightGrainSuper"
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_marginStart="3dp"
                                android:src="@drawable/right_grain" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/priceAndValidityView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="AI功能畅享用"
                            android:textColor="@android:color/white"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <!-- 三角形装饰 -->
                    <ImageView
                        android:id="@+id/triangleSuper"
                        android:layout_width="12dp"
                        android:layout_height="8dp"
                        android:layout_marginBottom="6dp"
                        android:src="@drawable/triangle"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="@+id/vip_info_layout"
                        app:layout_constraintStart_toStartOf="@+id/vip_info_layout" />

                    <androidx.constraintlayout.widget.Guideline
                        android:id="@+id/guideline_vertical_50"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        app:layout_constraintGuide_percent="0.5" />

                    <LinearLayout
                        android:id="@+id/discount_info_layout"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="20dp"
                        android:gravity="center"
                        android:orientation="vertical"
                        app:layout_constraintBottom_toBottomOf="@+id/vip_info_layout"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/guideline_vertical_50"
                        app:layout_constraintTop_toTopOf="@+id/vip_info_layout">

                        <LinearLayout
                            android:id="@+id/discountTitleLayout"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center_vertical"
                            android:orientation="horizontal">

                            <ImageView
                                android:id="@+id/leftGrainDiscount"
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_marginEnd="3dp"
                                android:src="@drawable/left_grain"
                                android:visibility="invisible" />

                            <TextView
                                android:id="@+id/discountView"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="折扣会员卡"
                                android:textColor="#FFFFFF"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <ImageView
                                android:id="@+id/rightGrainDiscount"
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:layout_marginStart="3dp"
                                android:src="@drawable/right_grain"
                                android:visibility="invisible" />
                        </LinearLayout>


                        <TextView
                            android:id="@+id/enjoyCountView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="课程折扣8.8折"
                            android:textColor="#FFFFFF"
                            android:textSize="12sp" />
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/triangleDiscount"
                        android:layout_width="12dp"
                        android:layout_height="8dp"
                        android:layout_marginBottom="6dp"
                        android:src="@drawable/triangle"
                        android:visibility="invisible"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="@+id/discount_info_layout"
                        app:layout_constraintStart_toStartOf="@+id/discount_info_layout" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="21dp"
                    android:orientation="horizontal"
                    android:paddingHorizontal="20dp">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:baselineAligned="false"
                        android:gravity="bottom"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:id="@+id/singlePurchaseCard"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="0dp"
                            app:cardPreventCornerOverlap="false"
                            app:cardUseCompatPadding="true">


                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <!--
                                  主要内容区域，使用LinearLayout。
                                  - layout_marginTop将整个白色区域向下推，为顶部的红色标签留出空间。
                                  - paddingTop为内部的 "单独购买" 等文本提供了与顶部边缘的间距。
                                -->
                                <LinearLayout
                                    android:id="@+id/content_area"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="16dp"
                                    android:background="@drawable/bg_promo_tag_solidyellow"
                                    android:gravity="center_horizontal"
                                    android:orientation="vertical"
                                    android:paddingTop="20dp"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="单独购买"
                                        android:textColor="@android:color/black"
                                        android:textSize="16sp"
                                        android:textStyle="bold" />

                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="8dp"
                                        android:gravity="bottom"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="¥"
                                            android:textColor="@android:color/black"
                                            android:textSize="18sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="17.9"
                                            android:textColor="@android:color/black"

                                            android:textSize="32sp"
                                            android:textStyle="bold" />
                                    </LinearLayout>

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="4dp"
                                        android:text="原价¥19.9"
                                        android:textColor="#9E9E9E"
                                        android:textSize="12sp"
                                        tools:paint_flags="strike_thru_text" /> <!-- 删除线效果 -->

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="12dp"
                                        android:background="@drawable/bg_promo_tag_danyellow"
                                        android:gravity="center"
                                        android:paddingVertical="8dp"
                                        android:text="立省¥2"
                                        android:textColor="#FE5656" />

                                </LinearLayout>


                                <LinearLayout
                                    android:id="@+id/promo_tag_container"
                                    android:layout_width="wrap_content"
                                    android:layout_height="22dp"
                                    android:layout_marginStart="12dp"
                                    android:layout_marginTop="-11dp"
                                    android:background="@drawable/bg_promo_tag_red"
                                    android:gravity="center"
                                    android:orientation="horizontal"
                                    android:paddingHorizontal="18dp"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="@+id/content_area">


                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="限时优惠2元"
                                        android:textColor="@android:color/white"
                                        android:textSize="12sp"
                                        android:textStyle="bold" />


                                    <TextView
                                        android:id="@+id/countDownTimeView"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginStart="2dp"
                                        android:text="23:59:59"
                                        android:textColor="@android:color/white"
                                        android:textSize="9sp" />

                                    <TextView
                                        android:layout_width="wrap_content"

                                        android:layout_height="wrap_content"
                                        android:text="后失效"
                                        android:textColor="@android:color/white"
                                        android:textSize="9sp" />

                                </LinearLayout>

                            </androidx.constraintlayout.widget.ConstraintLayout>

                        </androidx.cardview.widget.CardView>

                        <!-- 卡片间的水平间距 -->
                        <View
                            android:layout_width="15dp"
                            android:layout_height="0dp" />

                        <!-- 拼团购买卡片 (已重构为ConstraintLayout以匹配大小) -->
                        <androidx.cardview.widget.CardView
                            android:id="@+id/groupPurchaseCard"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="0dp"
                            app:cardPreventCornerOverlap="false"
                            app:cardUseCompatPadding="true"> <!-- 高亮背景色 -->

                            <androidx.constraintlayout.widget.ConstraintLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <!--
                                  主要内容区域，使用LinearLayout。
                                  - layout_marginTop将整个区域向下推，为顶部的标签留出空间（即使这里没有标签，也要保留这个边距以对齐）。
                                  - paddingTop为内部的 "拼团购买" 等文本提供了与顶部边缘的间距。
                                -->
                                <LinearLayout
                                    android:id="@+id/group_purchase_content_area"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="16dp"
                                    android:background="@drawable/bg_promo_tag_gradingyellows"
                                    android:gravity="center_horizontal"
                                    android:orientation="vertical"
                                    android:paddingTop="20dp"
                                    app:layout_constraintBottom_toBottomOf="parent"
                                    app:layout_constraintEnd_toEndOf="parent"
                                    app:layout_constraintStart_toStartOf="parent"
                                    app:layout_constraintTop_toTopOf="parent">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:text="拼团购买"
                                        android:textColor="#E65100"
                                        android:textSize="16sp"
                                        android:textStyle="bold" />

                                    <LinearLayout
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="8dp"
                                        android:gravity="bottom"
                                        android:orientation="horizontal">

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="¥"
                                            android:textColor="#F44336"
                                            android:textSize="18sp" />

                                        <TextView
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="14.9"
                                            android:textColor="#F44336"
                                            android:textSize="32sp"
                                            android:textStyle="bold" />
                                    </LinearLayout>

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="4dp"
                                        android:text="原价¥19.9"
                                        android:textColor="#9E9E9E"
                                        android:textSize="12sp"
                                        tools:paint_flags="strike_thru_text" />

                                    <TextView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginTop="12dp"
                                        android:background="@drawable/bg_promo_tag_danyellow"
                                        android:gravity="center"
                                        android:paddingVertical="8dp"
                                        android:text="立省¥5"
                                        android:textColor="#E65100" />
                                </LinearLayout>
                            </androidx.constraintlayout.widget.ConstraintLayout>
                        </androidx.cardview.widget.CardView>

                    </LinearLayout>
                </LinearLayout>

                <!-- 3. 会员权益表格 -->
                <TextView
                    android:id="@+id/openCountView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="30dp"
                    android:text="超级折扣会员卡享受8大权益"
                    android:textColor="@android:color/black"
                    android:textSize="16sp"
                    android:textStyle="bold" />


                <FrameLayout
    android:id="@+id/tableContainer"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="15dp"
    android:layout_marginTop="15dp"
    android:layout_marginBottom="24dp"
    android:clipChildren="false">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_table_container_border"
        android:paddingTop="20dp"
        android:paddingBottom="10dp"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/vipTableLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:baselineAligned="false"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:clipChildren="false">

            <!-- Privilege Column -->
            <LinearLayout
                android:id="@+id/privilege_column"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.2"
                android:orientation="vertical"
                android:paddingVertical="10dp"
                android:gravity="center_horizontal"/>

            <!-- Super VIP Column -->
            <LinearLayout
                android:id="@+id/super_vip_column"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1.1"
                android:layout_marginHorizontal="5dp"
                android:layout_marginTop="-10dp"
                android:background="@drawable/bg_super_vip_highlight"
                android:orientation="vertical"
                android:paddingVertical="20dp"
                android:gravity="center_horizontal"/>

            <!-- VIP Column -->
            <LinearLayout
                android:id="@+id/vip_column"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:paddingVertical="10dp"
                android:gravity="center_horizontal"/>
                
            <!-- Regular User Column -->
            <LinearLayout
                android:id="@+id/regular_user_column"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:paddingVertical="10dp"
                android:gravity="center_horizontal"/>
        </LinearLayout>
    </LinearLayout>
</FrameLayout>


            </LinearLayout>
        </androidx.core.widget.NestedScrollView>


        <LinearLayout
            android:id="@+id/bottom_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@android:color/white"
            android:orientation="vertical"
            android:padding="13dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">


            <!--
              "立即拼团" 按钮 (使用LinearLayout模拟)。
              - layout_height="45dp" 严格遵循设计图规范。
              - gravity="center" 确保内部的两行文本在45dp的高度内垂直居中。
            -->
            <LinearLayout
                android:id="@+id/openGroupVipLayout"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@drawable/bg_gradient_button_red"
                android:clickable="true"
                android:focusable="true"
                android:gravity="center"
                android:orientation="vertical">

                <!-- 主标题 -->
                <TextView
                    android:id="@+id/openGroupVipView"
                    style="@style/normalText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:duplicateParentState="true"
                    android:text="¥12.9 立即拼团"
                    android:textColor="@android:color/white"
                    android:textSize="18sp" />

                <!-- 副标题 -->
                <TextView
                    android:id="@+id/openGroupSubView"
                    style="@style/lightText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:alpha="1"
                    android:duplicateParentState="true"
                    android:text="邀请1名新用户"
                    android:textColor="@android:color/white"
                    android:textSize="12sp" />

            </LinearLayout>

            <CheckBox
                android:id="@+id/serviceAgreementView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="8dp"
                android:button="@drawable/bg_circle_checkbox"
                android:checked="false"
                android:paddingStart="5dp"
                android:text=""
                android:textColor="#999999"
                android:textSize="12sp" />

        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>