<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:baselineAligned="false"
    android:orientation="horizontal"
    android:paddingHorizontal="4dp"
    android:paddingVertical="2dp">

    <!-- 列1: 特权 -->
    <LinearLayout
        android:id="@+id/privilegeColumn"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1.1"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvPrivilege"
            style="@style/Table.Cell.Privilege"
            android:text="特权" />
    </LinearLayout>

    <!-- 列2: 超级会员卡 (高亮列) -->
    <LinearLayout
        android:id="@+id/superVipColumn"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:layout_weight="1"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvSuperVip"
            style="@style/Table.Cell.Highlight"
            android:text="超级会员卡"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/ivSuperVip"
            style="@style/Table.Cell.Icon"
            android:visibility="gone" />
    </LinearLayout>

    <!-- 列3: 会员卡 -->
    <LinearLayout
        android:id="@+id/vipColumn"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:gravity="center"
        android:orientation="vertical">
        
        <TextView
            android:id="@+id/tvVip"
            style="@style/Table.Cell.Default"
            android:text="会员卡"
            android:visibility="gone" />
            
        <ImageView
            android:id="@+id/ivVip"
            style="@style/Table.Cell.Icon"
            android:visibility="gone" />
    </LinearLayout>

    <!-- 列4: 普通用户 -->
    <LinearLayout
        android:id="@+id/regularColumn"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvRegular"
            style="@style/Table.Cell.Default"
            android:text="普通用户"
            android:visibility="gone" />
            
        <ImageView
            android:id="@+id/ivRegular"
            style="@style/Table.Cell.Icon"
            android:visibility="gone" />
    </LinearLayout>

</LinearLayout>
